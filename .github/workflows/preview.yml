name: Preview Build

permissions:
  contents: write

on:
  push:
    branches:
      - '*'
    tags-ignore:
      - 'v*'
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to build preview from'
        required: false
        default: 'main'

env:
  PYTHON_VERSION: '3.13'
  NUITKA_VERSION: '2.7.12'
  UPX_VERSION: '5.0.2'

jobs:
  cleanup-old-previews:
    name: Cleanup old preview releases
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Delete old preview releases
      uses: actions/github-script@v7
      with:
        script: |
          const { data: releases } = await github.rest.repos.listReleases({
            owner: context.repo.owner,
            repo: context.repo.repo,
            per_page: 100
          });
          
          const previewReleases = releases.filter(release => 
            release.prerelease && 
            (release.tag_name.includes('dev-') || 
             release.tag_name.includes('preview-') ||
             release.tag_name.includes('feature-'))
          );
          
          // Keep only the latest 5 preview releases
          const releasesToDelete = previewReleases.slice(5);
          
          for (const release of releasesToDelete) {
            console.log(`Deleting old preview release: ${release.tag_name}`);
            try {
              await github.rest.repos.deleteRelease({
                owner: context.repo.owner,
                repo: context.repo.repo,
                release_id: release.id
              });
              
              // Also delete the tag
              await github.rest.git.deleteRef({
                owner: context.repo.owner,
                repo: context.repo.repo,
                ref: `tags/${release.tag_name}`
              });
            } catch (error) {
              console.log(`Failed to delete ${release.tag_name}: ${error.message}`);
            }
          }

  build-preview:
    name: Build Preview on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    needs: cleanup-old-previews
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: windows-latest
            platform: windows
            arch: x64
            executable_suffix: .exe
          - os: ubuntu-latest
            platform: linux
            arch: x64
            executable_suffix: ''
          - os: macos-latest
            platform: macos
            arch: x64
            executable_suffix: ''

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        ref: ${{ github.event.inputs.branch || github.ref }}
        fetch-depth: 0

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'

    - name: Setup MSVC environment (Windows)
      if: matrix.platform == 'windows'
      uses: ilammy/msvc-dev-cmd@v1
      with:
        arch: x64

    - name: Install system dependencies (Ubuntu)
      if: matrix.platform == 'linux'
      run: |
        sudo apt-get update
        sudo apt-get install -y build-essential zlib1g-dev libffi-dev upx-ucl

    - name: Install system dependencies (macOS)
      if: matrix.platform == 'macos'
      run: |
        brew install zlib upx

    - name: Install system dependencies (Windows)
      if: matrix.platform == 'windows'
      run: |
        # 安装 UPX
        $upxVersion = "${{ env.UPX_VERSION }}"
        $upxUrl = "https://github.com/upx/upx/releases/download/v$upxVersion/upx-$upxVersion-win64.zip"
        Invoke-WebRequest -Uri $upxUrl -OutFile "upx.zip"
        Expand-Archive -Path "upx.zip" -DestinationPath "."
        $upxPath = "upx-$upxVersion-win64/upx.exe"
        Move-Item $upxPath "C:/Windows/System32/upx.exe"
        # 验证安装
        upx --version

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install nuitka==${{ env.NUITKA_VERSION }}
        pip install -r requirements.txt

    - name: Get preview version
      id: version
      shell: bash
      run: |
        BRANCH_NAME=${GITHUB_REF#refs/heads/}
        BRANCH_NAME=${BRANCH_NAME//\//-}  # Replace / with -
        COMMIT_HASH=$(git rev-parse --short HEAD)
        
        if [[ "$BRANCH_NAME" == "develop" ]]; then
          VERSION="dev-${COMMIT_HASH}"
        else
          VERSION="preview-${BRANCH_NAME}-${COMMIT_HASH}"
        fi
        
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "branch=$BRANCH_NAME" >> $GITHUB_OUTPUT
        echo "Building preview version: $VERSION"

    - name: Build
      run: |
        python build.py --version ${{ steps.version.outputs.version }} --clean

    - name: Test executable
      shell: bash
      run: |
        cd dist
        if [[ "${{ matrix.platform }}" == "windows" ]]; then
          ./EdgeCLI.exe --version || echo "Version check failed, but executable exists"
        else
          ./EdgeCLI --version || echo "Version check failed, but executable exists"
        fi
        echo "✅ Executable test completed"

    - name: Create archive
      shell: bash
      run: |
        cd dist
        if [[ "${{ matrix.platform }}" == "windows" ]]; then
          7z a -tzip EdgeCLI-${{ steps.version.outputs.version }}-${{ matrix.platform }}-${{ matrix.arch }}.zip EdgeCLI${{ matrix.executable_suffix }}
        else
          tar -czf EdgeCLI-${{ steps.version.outputs.version }}-${{ matrix.platform }}-${{ matrix.arch }}.tar.gz EdgeCLI${{ matrix.executable_suffix }}
        fi

    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: EdgeCLI-preview-${{ matrix.platform }}-${{ matrix.arch }}
        path: |
          dist/EdgeCLI-${{ steps.version.outputs.version }}-${{ matrix.platform }}-${{ matrix.arch }}.*
        retention-days: 7
